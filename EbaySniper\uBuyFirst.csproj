<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="12.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props" Condition="Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props')" />
  <Import Project="..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props" Condition="Exists('..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props')" />
  <Import Project="..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props" Condition="Exists('..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props')" />
  <Import Project="..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props" Condition="Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" />
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{5406DF9B-5C42-40D1-91C4-A8CEB40D2428}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>uBuyFirst</RootNamespace>
    <AssemblyName>uBuyFirst</AssemblyName>
    <TargetFrameworkVersion>v4.7.2</TargetFrameworkVersion>
    <SGenUseProxyTypes>false</SGenUseProxyTypes>
    <SGenPlatformTarget>$(Platform)</SGenPlatformTarget>
    <TargetFrameworkProfile>
    </TargetFrameworkProfile>
    <FileAlignment>512</FileAlignment>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <LangVersion>8.0</LangVersion>
    <Nullable>enable</Nullable>
    <GenerateResourceUsePreserializedResources>false</GenerateResourceUsePreserializedResources>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|x86' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>5</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <GenerateSerializationAssemblies>Auto</GenerateSerializationAssemblies>
    <SGenUseProxyTypes>false</SGenUseProxyTypes>
    <RunCodeAnalysis>false</RunCodeAnalysis>
    <CodeAnalysisRuleSet>BasicCorrectnessRules.ruleset</CodeAnalysisRuleSet>
    <DocumentationFile>
    </DocumentationFile>
    <LangVersion>latest</LangVersion>
    <Nullable>enable</Nullable>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|x86' ">
    <PlatformTarget>x86</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
    <UseVSHostingProcess>true</UseVSHostingProcess>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup>
    <NoWin32Manifest>true</NoWin32Manifest>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>uGrad.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <SignAssembly>true</SignAssembly>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
    <SGenUseProxyTypes>false</SGenUseProxyTypes>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>false</Prefer32Bit>
    <Nullable>enable</Nullable>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <ErrorReport>prompt</ErrorReport>
    <LangVersion>latest</LangVersion>
  </PropertyGroup>
  <PropertyGroup />
  <ItemGroup>
    <Reference Include="Accessibility" />
    <Reference Include="AutoUpdater.NET, Version=1.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\AutoUpdater.NET.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="CefSharp, Version=131.3.10.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
      <HintPath>..\packages\CefSharp.Common.131.3.10\lib\net462\CefSharp.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.Core, Version=131.3.10.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
      <HintPath>..\packages\CefSharp.Common.131.3.10\lib\net462\CefSharp.Core.dll</HintPath>
    </Reference>
    <Reference Include="CefSharp.WinForms, Version=131.3.10.0, Culture=neutral, PublicKeyToken=40c4b6fc221f4138, processorArchitecture=MSIL">
      <HintPath>..\packages\CefSharp.WinForms.131.3.10\lib\net462\CefSharp.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="DalSoft.RestClient, Version=1.0.10.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\DalSoft.RestClient.4.4.1\lib\netstandard2.0\DalSoft.RestClient.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.BonusSkins.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.DataAccess.v24.2.UI, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Drawing.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Data.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Office.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Pdf.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Pdf.v24.2.Drawing, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.RichEdit.v24.2.Export, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Printing.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.RichEdit.v24.2.Core, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Data.Desktop.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Utils.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraBars.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraEditors.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraGrid.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraLayout.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraPdfViewer.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.Images.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraRichEdit.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraTreeList.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraVerticalGrid.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
    </Reference>
    <Reference Include="DevExpress.XtraWizard.v24.2, Version=24.2.6.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="eBay.Service, Version=3.981.0.0, Culture=neutral, PublicKeyToken=1d9d786a5932eaf0, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\eBay.Service.dll</HintPath>
    </Reference>
    <Reference Include="eBay.Service.XmlSerializers, Version=3.1027.0.0, Culture=neutral, PublicKeyToken=1d9d786a5932eaf0, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\eBay.Service.XmlSerializers.dll</HintPath>
    </Reference>
    <Reference Include="eBay.Services, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\eBay.Services.dll</HintPath>
      <Private>False</Private>
    </Reference>
    <Reference Include="ExceptionReporter.WinForms">
      <HintPath>bin\Debug\ExceptionReporter.WinForms.dll</HintPath>
    </Reference>
    <Reference Include="INIFileParser, Version=2.5.2.0, Culture=neutral, PublicKeyToken=79af7b307b65cf3c, processorArchitecture=MSIL">
      <HintPath>..\packages\ini-parser.2.5.2\lib\net20\INIFileParser.dll</HintPath>
    </Reference>
    <Reference Include="IO.Ably, Version=1.2.11.0, Culture=neutral, PublicKeyToken=70c9e5c3d2c68b16, processorArchitecture=MSIL">
      <HintPath>..\packages\ably.io.1.2.11\lib\net46\IO.Ably.dll</HintPath>
    </Reference>
    <Reference Include="IO.Ably.DeltaCodec, Version=1.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\ably.io.1.2.11\lib\net46\IO.Ably.DeltaCodec.dll</HintPath>
    </Reference>
    <Reference Include="J2N, Version=2.0.0.0, Culture=neutral, PublicKeyToken=f39447d697a969af, processorArchitecture=MSIL">
      <HintPath>..\packages\J2N.2.0.0-beta-0017\lib\net45\J2N.dll</HintPath>
    </Reference>
    <Reference Include="LicenseSpot.Framework, Version=3.9.0.0, Culture=neutral, PublicKeyToken=de24b71aae0b435d, processorArchitecture=MSIL">
      <HintPath>..\packages\LicenseSpot.Framework.3.9.0\lib\LicenseSpot.Framework.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net, Version=4.0.0.0, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.4.8.0-beta00015\lib\net45\Lucene.Net.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net.Analysis.Common, Version=4.0.0.0, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.Analysis.Common.4.8.0-beta00015\lib\net45\Lucene.Net.Analysis.Common.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net.Memory, Version=4.0.0.0, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.Memory.4.8.0-beta00015\lib\net45\Lucene.Net.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net.Queries, Version=4.0.0.0, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.Queries.4.8.0-beta00015\lib\net45\Lucene.Net.Queries.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net.QueryParser, Version=4.0.0.0, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.QueryParser.4.8.0-beta00015\lib\net45\Lucene.Net.QueryParser.dll</HintPath>
    </Reference>
    <Reference Include="Lucene.Net.Sandbox, Version=4.0.0.0, Culture=neutral, PublicKeyToken=85089178b9ac3181, processorArchitecture=MSIL">
      <HintPath>..\packages\Lucene.Net.Sandbox.4.8.0-beta00015\lib\net45\Lucene.Net.Sandbox.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=9.0.0.5, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.5\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Extensions.Configuration, Version=9.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.9.0.2\lib\net462\Microsoft.Extensions.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Abstractions, Version=9.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Abstractions.9.0.2\lib\net462\Microsoft.Extensions.Configuration.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Configuration.Binder, Version=9.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Configuration.Binder.9.0.2\lib\net462\Microsoft.Extensions.Configuration.Binder.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.9.0.5\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Http, Version=9.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Http.9.0.2\lib\net462\Microsoft.Extensions.Http.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging, Version=9.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.9.0.2\lib\net462\Microsoft.Extensions.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=9.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.9.0.2\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.9.0.5\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=9.0.0.5, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.9.0.5\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.WindowsAzure.Configuration, Version=3.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.WindowsAzure.ConfigurationManager.3.2.3\lib\net40\Microsoft.WindowsAzure.Configuration.dll</HintPath>
    </Reference>
    <Reference Include="MQTTnet, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
      <HintPath>..\packages\MQTTnet.4.3.7.1207\lib\net461\MQTTnet.dll</HintPath>
    </Reference>
    <Reference Include="MQTTnet.Extensions.ManagedClient, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
      <HintPath>..\packages\MQTTnet.Extensions.ManagedClient.4.3.7.1207\lib\net461\MQTTnet.Extensions.ManagedClient.dll</HintPath>
    </Reference>
    <Reference Include="MQTTnet.Extensions.WebSocket4Net, Version=4.3.7.1207, Culture=neutral, PublicKeyToken=fdb7629f2e364a63, processorArchitecture=MSIL">
      <HintPath>..\packages\MQTTnet.Extensions.WebSocket4Net.4.3.7.1207\lib\net461\MQTTnet.Extensions.WebSocket4Net.dll</HintPath>
    </Reference>
    <Reference Include="MsgPack, Version=1.0.0.0, Culture=neutral, PublicKeyToken=a2625990d5dc0167, processorArchitecture=MSIL">
      <HintPath>..\packages\MsgPack.Cli.1.0.1\lib\net46\MsgPack.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.1\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=5.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.5.4.0\lib\net46\NLog.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="PushbulletSharp, Version=3.1.0.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>bin\Debug\PushbulletSharp.dll</HintPath>
    </Reference>
    <Reference Include="Shopping">
      <HintPath>..\..\Shopping\Shopping\bin\Debug\Shopping.dll</HintPath>
    </Reference>
    <Reference Include="SimpleGoogleAnalytics, Version=1.1.6.0, Culture=neutral, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\SimpleGoogleAnalytics1\GoogleAnalytics\bin\Debug\netstandard2.0\SimpleGoogleAnalytics.dll</HintPath>
    </Reference>
    <Reference Include="SumoLogic.Logging.Common, Version=1.0.1.8, Culture=neutral, PublicKeyToken=6b03a58229215ab3, processorArchitecture=MSIL">
      <HintPath>..\packages\SumoLogic.Logging.Common.1.0.1.8\lib\net45\SumoLogic.Logging.Common.dll</HintPath>
    </Reference>
    <Reference Include="SumoLogic.Logging.NLog, Version=1.0.1.8, Culture=neutral, PublicKeyToken=6b03a58229215ab3, processorArchitecture=MSIL">
      <HintPath>..\packages\SumoLogic.Logging.NLog.1.0.1.8\lib\net45\SumoLogic.Logging.NLog.dll</HintPath>
    </Reference>
    <Reference Include="SuperSocket.ClientEngine, Version=0.10.0.0, Culture=neutral, PublicKeyToken=ee9af13f57f00acc, processorArchitecture=MSIL">
      <HintPath>..\packages\SuperSocket.ClientEngine.Core.0.10.0\lib\net45\SuperSocket.ClientEngine.dll</HintPath>
    </Reference>
    <Reference Include="System">
      <HintPath>..\..\..\..\..\Program Files (x86)\Reference Assemblies\Microsoft\Framework\.NETFramework\v4.5\System.dll</HintPath>
    </Reference>
    <Reference Include="System.AppContext, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.AppContext.4.3.0\lib\net463\System.AppContext.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.Collections.Specialized, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
      <HintPath>..\packages\System.Collections.Specialized.4.3.0\lib\net46\System.Collections.Specialized.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Annotations, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ComponentModel.Annotations.5.0.0\lib\net461\System.ComponentModel.Annotations.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Console, Version=4.0.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Console.4.3.1\lib\net46\System.Console.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Data">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.Linq" />
    <Reference Include="System.Data.SQLite, Version=1.0.119.0, Culture=neutral, PublicKeyToken=db937bc2d44ff139, processorArchitecture=MSIL">
      <HintPath>..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\lib\net46\System.Data.SQLite.dll</HintPath>
    </Reference>
    <Reference Include="System.Deployment" />
    <Reference Include="System.Design" />
    <Reference Include="System.IO.Compression">
      <HintPath>..\packages\System.IO.Compression.4.3.0\lib\net46\System.IO.Compression.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.FileSystem">
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression.ZipFile, Version=*******, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Compression.ZipFile.4.3.0\lib\net46\System.IO.Compression.ZipFile.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.4.3.0\lib\net46\System.IO.FileSystem.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.FileSystem.Primitives, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.Primitives.4.3.0\lib\net46\System.IO.FileSystem.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.4.3.0\lib\net463\System.Linq.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Linq.Expressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Linq.Expressions.4.3.0\lib\net463\System.Linq.Expressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.WinHttpHandler, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.WinHttpHandler.6.0.1\lib\net461\System.Net.Http.WinHttpHandler.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.NameResolution, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
      <Private>True</Private>
      <HintPath>..\packages\System.Net.NameResolution.4.3.0\lib\net46\System.Net.NameResolution.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Security, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <Private>True</Private>
      <Private>True</Private>
      <HintPath>..\packages\System.Net.Security.4.3.0\lib\net46\System.Net.Security.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Sockets, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Sockets.4.3.0\lib\net46\System.Net.Sockets.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Reflection, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Reflection.4.3.0\lib\net462\System.Reflection.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Resources.Extensions">
      <HintPath>..\packages\System.Resources.Extensions.9.0.5\lib\net462\System.Resources.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.Extensions, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.Extensions.4.3.1\lib\net462\System.Runtime.Extensions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.4.3.0\lib\net463\System.Runtime.InteropServices.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.Remoting" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Runtime.Serialization.Formatters.Soap" />
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.1\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.2, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.2\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Drawing">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Channels, Version=7.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Channels.7.0.0\lib\net462\System.Threading.Channels.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks" />
    <Reference Include="System.Threading.Tasks.Extensions, Version=4.2.1.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.0\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xaml" />
    <Reference Include="System.Xml">
      <Private>False</Private>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Xml.ReaderWriter, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Xml.ReaderWriter.4.3.1\lib\net46\System.Xml.ReaderWriter.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="Telegram.Bot, Version=18.0.0.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Telegram.Bot.18.0.0\lib\netstandard2.0\Telegram.Bot.dll</HintPath>
    </Reference>
    <Reference Include="Telegram.Bot.Extensions.Polling, Version=1.0.2.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Telegram.Bot.Extensions.Polling.1.0.2\lib\netstandard2.0\Telegram.Bot.Extensions.Polling.dll</HintPath>
    </Reference>
    <Reference Include="UIAutomationClient" />
    <Reference Include="WebSocket4Net, Version=0.15.2.11, Culture=neutral, PublicKeyToken=eb4e154b696bf72a, processorArchitecture=MSIL">
      <HintPath>..\packages\WebSocket4Net.0.15.2\lib\net45\WebSocket4Net.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AI\AiAnalysis.cs" />
    <Compile Include="AI\GeminiService.cs" />
    <Compile Include="AI\UrlJsonService.cs" />
    <Compile Include="AlertOptionsClass.cs" />
    <Compile Include="API\ShoppingAPI\PricingServiceShoppingAPI.cs" />
    <Compile Include="API\ShoppingAPI\ShoppingAPIJson.cs" />
    <Compile Include="Auth\Authenticator.cs" />
    <Compile Include="BrowseAPI\BrowseAPI.cs" />
    <Compile Include="BrowseAPI\BrowseAPIParser.cs" />
    <Compile Include="BrowseAPI\BrowseAPISearchBuilder.cs" />
    <Compile Include="BrowseAPI\CategoryService.cs" />
    <Compile Include="CefBrowser\CefAssemblyResolver.cs" />
    <Compile Include="CefBrowser\CefBrowserManager.cs" />
    <Compile Include="Data\EmptyLineBehavior.cs" />
    <Compile Include="ExternalData\CustomCefRequestHandler.cs" />
    <Compile Include="Filters\ConcreteFilterActions.cs" />
    <Compile Include="Filters\FilterActions.cs" />
    <Compile Include="FormAboutExperiment.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormAboutExperiment.Designer.cs">
      <DependentUpon>FormAboutExperiment.cs</DependentUpon>
    </Compile>
    <Compile Include="FormExternalData.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormExternalData.Designer.cs">
      <DependentUpon>FormExternalData.cs</DependentUpon>
    </Compile>
    <Compile Include="ExternalData\ExternalDataManager.cs" />
    <Compile Include="Filters\CriteriaColumnExtractor.cs" />
    <Compile Include="Filters\CustomFilterColumn.cs" />
    <Compile Include="Filters\FormatRuleManager.cs" />
    <Compile Include="Filters\XFilterManager.cs" />
    <Compile Include="FormBid.BestOffer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBid.Layout.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBid.OrderAPI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBid.Other.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBidConfirmation.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBidConfirmation.Designer.cs">
      <DependentUpon>FormBidConfirmation.cs</DependentUpon>
    </Compile>
    <Compile Include="FormSyncSearchTerms.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormSyncSearchTerms.Designer.cs">
      <DependentUpon>FormSyncSearchTerms.cs</DependentUpon>
    </Compile>
    <Compile Include="FormTelegram.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormTelegram.Designer.cs">
      <DependentUpon>FormTelegram.cs</DependentUpon>
    </Compile>
    <Compile Include="FormShortcuts.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormShortcuts.Designer.cs">
      <DependentUpon>FormShortcuts.cs</DependentUpon>
    </Compile>
    <Compile Include="GUI\CategoryLookup.cs" />
    <Compile Include="CustomClasses\CustomBindingList.cs" />
    <Compile Include="EndNowFinder.cs" />
    <Compile Include="Grid\TopRowFocus.cs" />
    <Compile Include="GUI\Browser.cs" />
    <Compile Include="GUI\Categories.cs" />
    <Compile Include="GUI\Category.cs" />
    <Compile Include="API\TradingAPI\CategoryService.cs" />
    <Compile Include="GUI\FlyoutPanelSnackBar.cs" />
    <Compile Include="GUI\WebView.cs" />
    <Compile Include="FormIgnoreSellers.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormIgnoreSellers.Designer.cs">
      <DependentUpon>FormIgnoreSellers.cs</DependentUpon>
    </Compile>
    <Compile Include="Seller\SellerHelper.cs" />
    <Compile Include="Images\ImageCache.cs" />
    <Compile Include="Images\ImageCleaner.cs" />
    <Compile Include="Images\PicManager.cs" />
    <Compile Include="Item\CacheManager.cs" />
    <Compile Include="Item\CategorySpecific.cs" />
    <Compile Include="Data\CsvFileWriter.cs" />
    <Compile Include="Data\DataList.cs" />
    <Compile Include="Item\DataListParser.cs" />
    <Compile Include="Item\FiaParser.cs" />
    <Compile Include="Item\UniversalItemParser.cs" />
    <Compile Include="KeywordTools.cs" />
    <Compile Include="Filters\MyDescriptor.cs" />
    <Compile Include="Filters\MyUnboundColumnExpressionEditorForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="License\Limits.cs" />
    <Compile Include="LoggingContractResolver.cs" />
    <Compile Include="Models\SellerUser.cs" />
    <Compile Include="MQTT\MQTTManager.cs" />
    <Compile Include="Network\AblyClient2.cs" />
    <Compile Include="Network\AblyLogger.cs" />
    <Compile Include="Network\ConsoleEx.cs" />
    <Compile Include="Images\ImageProcessor.cs" />
    <Compile Include="Network\NetTools.Http2CustomHandler.cs" />
    <Compile Include="Network\UrlFetcher.cs" />
    <Compile Include="Network\WebSocketClient.cs" />
    <Compile Include="Parsing\DateParser.cs" />
    <Compile Include="Pricing\CurrencyConverter.cs" />
    <Compile Include="Pricing\ItemShipping.cs" />
    <Compile Include="Pricing\ShippingParserTradingAPI.cs" />
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Resources.resx</DependentUpon>
    </Compile>
    <Compile Include="Purchasing\AffiliateTool.cs" />
    <Compile Include="Purchasing\BestOfferCheckout.cs" />
    <Compile Include="Purchasing\BestOfferService.cs" />
    <Compile Include="Purchasing\HttpServiceHelper.cs" />
    <Compile Include="Purchasing\CreditCardCheckout.cs" />
    <Compile Include="Purchasing\Cookies\UnlockManager.cs" />
    <Compile Include="Purchasing\QuickPurchaseManager.cs" />
    <Compile Include="SearchTerms\SearchTermFetcher.cs" />
    <Compile Include="SearchTerms\SearchTermManager.cs" />
    <Compile Include="Search\RequestQueueManager1.cs" />
    <Compile Include="Search\SearchConfigManager1.cs" />
    <Compile Include="Seller\CountryData.cs" />
    <Compile Include="Seller\SellerUserRaw.cs" />
    <Compile Include="Services\Caching\ICacheService.cs" />
    <Compile Include="Services\Caching\InMemoryCacheService.cs" />
    <Compile Include="Services\CountryService.cs" />
    <Compile Include="Services\ICountryService.cs" />
    <Compile Include="Services\ISellerService.cs" />
    <Compile Include="Services\SellerService.cs" />
    <Compile Include="Restocker\Models\SyncHistory.cs" />
    <Compile Include="Restocker\Models\SyncResult.cs" />
    <Compile Include="Restocker\Services\PurchaseExecutionService.cs" />
    <Compile Include="Restocker\Services\CaptchaCooldownManager.cs" />
    <Compile Include="Restocker\Services\IKeywordDataService.cs" />
    <Compile Include="Restocker\Services\KeywordDataService.cs" />
    <Compile Include="Restocker\Models\KeywordSnapshot.cs" />
    <Compile Include="RestockReporting\Models\ItemProcessingContext.cs" />
    <Compile Include="RestockReporting\Services\IItemHistoryLogger.cs" />
    <Compile Include="RestockReporting\Services\IItemHistoryExporter.cs" />
    <Compile Include="RestockReporting\Services\ItemHistoryOptions.cs" />
    <Compile Include="RestockReporting\Services\FileItemHistoryLogger.cs" />
    <Compile Include="RestockReporting\Services\ItemHistoryExporter.cs" />
    <Compile Include="RestockReporting\Services\DataListMapper.cs" />
    <Compile Include="Filters\RestockFilterAction.cs" />
    <Compile Include="SkuManager\SkuPath.cs" />
    <Compile Include="SkuManager\PythonProcessManager.cs" />
    <Compile Include="SubSearch\ChildTerm.cs" />
    <Compile Include="Search\FoundItem.cs" />
    <Compile Include="SubSearch\SubSearch.cs" />
    <Compile Include="Telegram\BotCommandHandler.cs" />
    <Compile Include="Telegram\ITelegramSender.cs" />
    <Compile Include="Telegram\ItemIdShortener.cs" />
    <Compile Include="Telegram\MessageTemplateManager.cs" />
    <Compile Include="Telegram\TelegramSender.cs" />
    <Compile Include="Pricing\CurrencyAmount.cs" />
    <Compile Include="Pricing\PricingServiceBrowseAPI.cs" />
    <Compile Include="Purchasing\BuyingService.cs" />
    <Compile Include="Purchasing\Cookies\CookieManager.cs" />
    <Compile Include="Purchasing\PaymentLogger.cs" />
    <Compile Include="Purchasing\CreditCardService.cs" />
    <Compile Include="Search\BatchLinq.cs" />
    <Compile Include="BrowseAPI\BrowseAPINetwork.cs" />
    <Compile Include="Search\FIA\FiaService.cs" />
    <Compile Include="Search\OutOfStockSearcher.cs" />
    <Compile Include="Search\SearchService.cs" />
    <Compile Include="Search\SearchSource.cs" />
    <Compile Include="Search\Status\GetItemsStatus.cs" />
    <Compile Include="Search\Status\RowStatusUpdater.cs" />
    <Compile Include="Security\HttpControl.cs" />
    <Compile Include="Security\RsAtoPhpCryptography.cs" />
    <Compile Include="Prefs\Config.cs" />
    <Compile Include="Stats\Pixel.cs" />
    <Compile Include="Taxonomy\TaxonomyAPI.cs" />
    <Compile Include="Telegram\TokenBucket.cs" />
    <Compile Include="Time\DateTimeWithDiff.cs" />
    <Compile Include="Auth\EbayAccount.cs" />
    <Compile Include="Security\AeStoPhpCryptography.cs" />
    <Compile Include="Stats\Analytics.cs" />
    <Compile Include="Tools\ControlHelpers.cs" />
    <Compile Include="Tools\Decryptor.cs" />
    <Compile Include="Tools\ExM.cs" />
    <Compile Include="Filters\FilterAdapter.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Form1.Alerts.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Documents.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.EBaySearches.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.FilterEdit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Ribbon.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.SearchService.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Workspace.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="GUI\FocusRouter.cs" />
    <Compile Include="Item\ItemParser.cs" />
    <Compile Include="Item\ItemSpecifics.cs" />
    <Compile Include="Network\AlternativeTransports.cs" />
    <Compile Include="Network\SeerSearchTerm.cs" />
    <Compile Include="Network\SeerUser.cs" />
    <Compile Include="Network\StringCompressor.cs" />
    <Compile Include="Other\Folders.cs" />
    <Compile Include="FormAbout.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormAbout.Designer.cs">
      <DependentUpon>FormAbout.cs</DependentUpon>
    </Compile>
    <Compile Include="FormAlert.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormAlert.Designer.cs">
      <DependentUpon>FormAlert.cs</DependentUpon>
    </Compile>
    <Compile Include="FormSpecificsColumns.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormSpecificsColumns.Designer.cs">
      <DependentUpon>FormSpecificsColumns.cs</DependentUpon>
    </Compile>
    <Compile Include="FormEbayAccounts.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormEbayAccounts.Designer.cs">
      <DependentUpon>FormEbayAccounts.cs</DependentUpon>
    </Compile>
    <Compile Include="FormHighlightWords.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormHighlightWords.Designer.cs">
      <DependentUpon>FormHighlightWords.cs</DependentUpon>
    </Compile>
    <Compile Include="FormReportDialog.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormReportDialog.Designer.cs">
      <DependentUpon>FormReportDialog.cs</DependentUpon>
    </Compile>
    <Compile Include="FormSubscription.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormSubscription.designer.cs">
      <DependentUpon>FormSubscription.cs</DependentUpon>
    </Compile>
    <Compile Include="BGworker.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormXfilters.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormXfilters.designer.cs">
      <DependentUpon>FormXfilters.cs</DependentUpon>
    </Compile>
    <Compile Include="FormPushbullet.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormPushbullet.Designer.cs">
      <DependentUpon>FormPushbullet.cs</DependentUpon>
    </Compile>
    <Compile Include="CustomClasses\GetMultipleItemsClass.cs" />
    <Compile Include="Prefs\UserSettings.cs" />
    <Compile Include="Tools\MachineProfile.cs" />
    <Compile Include="Tools\Helpers.cs" />
    <Compile Include="Intl\CountryFilter.cs" />
    <Compile Include="Intl\CountryProvider.cs" />
    <Compile Include="Intl\EBaySite.cs" />
    <Compile Include="Keyword2Find.cs" />
    <Compile Include="Network\ApiService.cs" />
    <Compile Include="Network\NetTools.cs" />
    <Compile Include="Images\ImageTools.cs" />
    <Compile Include="Other\Loggers.cs" />
    <Compile Include="Other\Serializator.cs" />
    <Compile Include="Other\StandardAnalyzerFix.cs" />
    <Compile Include="Pricing\ItemPricing.cs" />
    <Compile Include="Pricing\Location.cs" />
    <Compile Include="Pricing\PricingService.cs" />
    <Compile Include="Prefs\ProgramState.cs" />
    <Compile Include="Purchasing\Placeoffer.cs" />
    <Compile Include="Network\PushbulletSender.cs" />
    <Compile Include="Search\FIA\FiaBuilder.cs" />
    <Compile Include="Security\SecurePhpConnection.cs" />
    <Compile Include="Tools\KeywordHelpers.cs" />
    <Compile Include="Tools\SerializableDictionary.cs" />
    <Compile Include="Tools\SettingsClass.cs" />
    <Compile Include="Time\TimeSync.cs" />
    <Compile Include="GUI\TrayManager.cs" />
    <Compile Include="CustomClasses\UnboundExpressionPanel.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Grid\GridBuilder.cs" />
    <Compile Include="Grid\GridViewEvents.cs" />
    <Compile Include="Tools\Stat.cs" />
    <Compile Include="API\TradingAPI\TradingAPIService.cs" />
    <Compile Include="FormTrialSubscriptionPrompt.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormTrialSubscriptionPrompt.designer.cs">
      <DependentUpon>FormTrialSubscriptionPrompt.cs</DependentUpon>
    </Compile>
    <Compile Include="Update\Upgrader.cs" />
    <Compile Include="ViewReporter.cs" />
    <Compile Include="Views\ColumnsManager.cs" />
    <Compile Include="Views\GuiChanger.cs" />
    <Compile Include="Views\MruManager.cs" />
    <Compile Include="Views\ResultsView.cs" />
    <Compile Include="Purchasing\BuyAPI\OrderApi.cs" />
    <Compile Include="Purchasing\BuyAPI\OrderService.cs" />
    <Compile Include="Other\FixedSizeQueue.cs" />
    <Compile Include="Properties\En-US.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>En-US.resx</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Form1.Treelist.cs">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.PushBullet.cs">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Start.cs">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormAuth.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormAuth.Designer.cs">
      <DependentUpon>FormAuth.cs</DependentUpon>
    </Compile>
    <Compile Include="FormBid.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FormBid.designer.cs">
      <DependentUpon>FormBid.cs</DependentUpon>
    </Compile>
    <Compile Include="License\LicenseUtility.cs" />
    <Compile Include="Other\PriorityQueue.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Search\QueryList.cs" />
    <Compile Include="Search\SearchStuff.cs" />
    <Compile Include="Settings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Network\AblyClient.cs" />
    <Compile Include="NewItem.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Update\Updater.cs" />
    <Compile Include="GUI\WindowFocus.cs" />
    <Compile Include="GUI\WorkspacePatcher.cs" />
    <Compile Include="Filters\XFilterClassChild.cs" />
    <Compile Include="Filters\XFilterClass.cs" />
    <Compile Include="Watchlist\ClipboardItemParser.cs" />
    <Compile Include="Watchlist\NotificationEventArgs.cs" />
    <Compile Include="Watchlist\WatchlistManager.cs" />
    <EmbeddedResource Include="FormAboutExperiment.resx">
      <DependentUpon>FormAboutExperiment.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormExternalData.resx">
      <DependentUpon>FormExternalData.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormAbout.resx">
      <DependentUpon>FormAbout.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormAlert.resx">
      <DependentUpon>FormAlert.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormBidConfirmation.resx">
      <DependentUpon>FormBidConfirmation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormIgnoreSellers.resx">
      <DependentUpon>FormIgnoreSellers.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormSyncSearchTerms.resx">
      <DependentUpon>FormSyncSearchTerms.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTelegram.resx">
      <DependentUpon>FormTelegram.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormShortcuts.resx">
      <DependentUpon>FormShortcuts.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormSpecificsColumns.resx">
      <DependentUpon>FormSpecificsColumns.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormEbayAccounts.resx">
      <DependentUpon>FormEbayAccounts.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormHighlightWords.resx">
      <DependentUpon>FormHighlightWords.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormReportDialog.resx">
      <DependentUpon>FormReportDialog.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormSubscription.resx">
      <DependentUpon>FormSubscription.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormXfilters.resx">
      <DependentUpon>FormXfilters.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FormPushbullet.resx">
      <DependentUpon>FormPushbullet.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\En-US.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>En-US.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormAuth.resx">
      <DependentUpon>FormAuth.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="FormBid.resx">
      <DependentUpon>FormBid.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <!-- <EmbeddedResource Include="Properties\licenses.licx" /> -->
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>PublicResXFileCodeGenerator</Generator>
      <SubType>Designer</SubType>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="FormTrialSubscriptionPrompt.resx">
      <DependentUpon>FormTrialSubscriptionPrompt.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="..\.editorconfig">
      <Link>.editorconfig</Link>
    </None>
    <None Include="app.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <SubType>Designer</SubType>
    </None>
    <None Include="app.manifest">
      <SubType>Designer</SubType>
    </None>
    <Compile Include="Prefs\ConnectionConfig.cs" />
    <None Include="packages.config" />
    <None Include="Properties\DataSources\FilterClass.datasource" />
    <None Include="Properties\DataSources\Form1.Items.datasource" />
    <None Include="Properties\DataSources\Form1.Keyword2Find.datasource" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <EmbeddedResource Include="runtime.lic" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\binbloop.wav" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.0,Profile=Client">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4 Client Profile %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RightNow10845.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="Resources\RightNow.gif" />
  </ItemGroup>
  <ItemGroup>
    <None Include="uGrad.png" />
    <None Include="Resources\RemoveIcon.ico" />
    <None Include="Resources\Cart16.png" />
    <None Include="Resources\MakeOffer.svg" />
    <None Include="Resources\Keywords.svg" />
    <None Include="Resources\Clear.svg" />
    <None Include="Resources\Start.svg" />
    <None Include="Resources\Terms.svg" />
    <None Include="Resources\CustomColumns.svg" />
    <None Include="Resources\Views.svg" />
    <None Include="Resources\Font.svg" />
    <None Include="Resources\Alert.svg" />
    <None Include="Resources\Workspaces.svg" />
    <None Include="Resources\Sync.svg" />
    <None Include="Resources\Add.svg" />
    <None Include="Resources\Remove.svg" />
    <None Include="Resources\Export.svg" />
    <None Include="Resources\Support.svg" />
    <None Include="Resources\Stop.svg" />
    <None Include="Resources\Eye.svg" />
    <None Include="Resources\Pushbullet.svg" />
    <None Include="Resources\MakeOfferDark.svg" />
    <None Include="Resources\ebay.jpg" />
    <None Include="Resources\Collapse.svg" />
    <None Include="Resources\Expand.svg" />
    <None Include="Resources\ColorPalette.svg" />
    <None Include="Resources\Settings.svg" />
    <None Include="Resources\NormalSize.svg" />
    <None Include="Resources\Paypal.svg" />
    <None Include="Resources\Shortcut.svg" />
    <None Include="Resources\Checkout.svg" />
    <None Include="Resources\SortAscending.svg" />
    <None Include="Resources\telegram.svg" />
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="j2n-icon-100x100.png" />
    <Content Include="j2n-icon-100x100.png" />
    <Content Include="LICENSE.txt" />
    <Content Include="LICENSE.txt" />
    <None Include="Resources\Reload.svg" />
    <None Include="Resources\SquareEmpty.svg" />
    <None Include="Resources\CreditCard.svg" />
    <None Include="Resources\CreditCard1.svg" />
    <None Include="Resources\CreditCard2.svg" />
    <None Include="Resources\CheckMark.svg" />
    <None Include="Resources\Ignore-Seller.svg" />
    <None Include="Resources\Blocked.svg" />
    <None Include="Resources\amazon.svg" />
    <None Include="Resources\SearchSoldOnEbay.svg" />
    <None Include="Resources\AddToWatchlist.svg" />
    <None Include="Resources\Ignore-Seller1.svg" />
    <Content Include="uGrad.ico" />
  </ItemGroup>
  <ItemGroup />
  <ItemGroup>
    <Analyzer Include="..\packages\Lucene.Net.4.8.0-beta00015\analyzers\dotnet\cs\Lucene.Net.CodeAnalysis.CSharp.dll" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <PropertyGroup>
    <PostBuildEvent>
    </PostBuildEvent>
  </PropertyGroup>
  <PropertyGroup>
    <SsdtUnitTestVersion>2.0</SsdtUnitTestVersion>
  </PropertyGroup>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets'))" />
    <Error Condition="!Exists('..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Net.Compilers.4.2.0\build\Microsoft.Net.Compilers.props'))" />
    <Error Condition="!Exists('..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\chromiumembeddedframework.runtime.win-x64.131.3.1\build\chromiumembeddedframework.runtime.win-x64.props'))" />
    <Error Condition="!Exists('..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\chromiumembeddedframework.runtime.win-x86.131.3.1\build\chromiumembeddedframework.runtime.win-x86.props'))" />
    <Error Condition="!Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.props'))" />
    <Error Condition="!Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets'))" />
    <Error Condition="!Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets'))" />
  </Target>
  <Import Project="..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets" Condition="Exists('..\packages\NETStandard.Library.2.0.3\build\netstandard2.0\NETStandard.Library.targets')" />
  <Import Project="..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets" Condition="Exists('..\packages\System.Runtime.WindowsRuntime.5.0.0-preview.5.20278.1\build\net461\System.Runtime.WindowsRuntime.targets')" />
  <Import Project="..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets" Condition="Exists('..\packages\CefSharp.Common.131.3.10\build\CefSharp.Common.targets')" />
  <Import Project="..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets" Condition="Exists('..\packages\Stub.System.Data.SQLite.Core.NetFramework.1.0.119.0\build\net46\Stub.System.Data.SQLite.Core.NetFramework.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>